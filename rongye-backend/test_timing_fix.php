<?php

// 使用ThinkPHP命令行模式
define('APP_PATH', __DIR__ . '/application/');
define('ROOT_PATH', __DIR__ . '/');
define('EXTEND_PATH', __DIR__ . '/extend/');
define('VENDOR_PATH', __DIR__ . '/vendor/');
define('RUNTIME_PATH', __DIR__ . '/runtime/');
define('LOG_PATH', __DIR__ . '/runtime/log/');
define('CACHE_PATH', __DIR__ . '/runtime/cache/');
define('TEMP_PATH', __DIR__ . '/runtime/temp/');
define('CONF_PATH', __DIR__ . '/application/');
define('CONF_EXT', '.php');
define('ENV_PREFIX', 'PHP_');
define('IS_CLI', true);

require_once __DIR__ . '/thinkphp/start.php';

use addons\shopro\library\Bonus;
use addons\shopro\library\UserActiveManager;
use think\Db;
use think\Log;

echo "=== 测试分润时序修复 ===\n\n";

try {
    // 1. 设置测试环境 - 将用户100001设置为未激活状态
    echo "1. 设置测试环境...\n";
    Db::name('user')->where('id', 100001)->update([
        'is_active' => 0,
        'active_expire_time' => 0
    ]);
    
    // 清除缓存
    UserActiveManager::clearActiveCache(100001);
    
    // 检查用户激活状态
    $user_before = Db::name('user')->where('id', 100001)->find();
    $is_active_before = UserActiveManager::isUserActive(100001, false);
    
    echo "用户100001修复前状态:\n";
    echo "- is_active: {$user_before['is_active']}\n";
    echo "- active_expire_time: {$user_before['active_expire_time']}\n";
    echo "- UserActiveManager::isUserActive: " . ($is_active_before ? '是' : '否') . "\n\n";
    
    // 2. 清除之前的分润记录
    echo "2. 清除之前的分润记录...\n";
    Db::name('shopro_user_wallet_log')
        ->where('ext', 'like', '%"order_id":108%')
        ->delete();
    
    // 重置用户累计分润
    Db::name('user')->where('id', 100001)->update(['total_bonus_received' => 0]);
    Db::name('user')->where('id', 100002)->update(['total_bonus_received' => 0]);
    Db::name('user')->where('id', 100003)->update(['total_bonus_received' => 0]);
    
    echo "已清除订单108的分润记录\n\n";
    
    // 3. 重新处理订单分润
    echo "3. 重新处理订单108的分润...\n";
    $result = Bonus::processOrderBonus(108);
    
    if ($result) {
        echo "分润处理成功！\n\n";
    } else {
        echo "分润处理失败！\n\n";
        return;
    }
    
    // 4. 检查修复后的状态
    echo "4. 检查修复后的状态...\n";
    
    // 检查用户激活状态
    $user_after = Db::name('user')->where('id', 100001)->find();
    $is_active_after = UserActiveManager::isUserActive(100001, false);
    
    echo "用户100001修复后状态:\n";
    echo "- is_active: {$user_after['is_active']}\n";
    echo "- active_expire_time: {$user_after['active_expire_time']}\n";
    echo "- UserActiveManager::isUserActive: " . ($is_active_after ? '是' : '否') . "\n";
    echo "- total_bonus_received: {$user_after['total_bonus_received']}\n\n";
    
    // 5. 检查分润记录
    echo "5. 检查分润记录...\n";
    $bonus_logs = Db::name('shopro_user_wallet_log')
        ->where('ext', 'like', '%"order_id":108%')
        ->where('type', 'money')
        ->order('user_id, createtime')
        ->select();
    
    $total_bonus = 0;
    foreach ($bonus_logs as $log) {
        $user_info = Db::name('user')->where('id', $log['user_id'])->find();
        echo "- 用户{$log['user_id']}({$user_info['username']}): {$log['event']} +{$log['amount']}元 - {$log['memo']}\n";
        $total_bonus += $log['amount'];
    }
    
    echo "\n总分润金额: {$total_bonus}元\n\n";
    
    // 6. 验证关键问题是否解决
    echo "6. 验证关键问题是否解决...\n";
    
    $buyer_bonus = Db::name('shopro_user_wallet_log')
        ->where('user_id', 100001)
        ->where('ext', 'like', '%"order_id":108%')
        ->where('type', 'money')
        ->sum('amount');
    
    $inviter_bonus = Db::name('shopro_user_wallet_log')
        ->where('user_id', 100002)
        ->where('ext', 'like', '%"order_id":108%')
        ->where('type', 'money')
        ->sum('amount');
    
    echo "关键验证结果:\n";
    echo "- 购买者(100001)是否获得团队奖励: " . ($buyer_bonus > 0 ? "是 ({$buyer_bonus}元)" : "否") . "\n";
    echo "- 邀请人(100002)是否获得直销奖励: " . ($inviter_bonus > 0 ? "是 ({$inviter_bonus}元)" : "否") . "\n";
    
    if ($buyer_bonus > 0 && $inviter_bonus > 0) {
        echo "\n✅ 时序问题修复成功！未激活用户购买后能正常获得分润。\n";
    } else {
        echo "\n❌ 时序问题仍然存在，需要进一步检查。\n";
    }
    
    echo "\n=== 测试完成 ===\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪: " . $e->getTraceAsString() . "\n";
}
