<?php

// 简单的分润触发脚本
require_once __DIR__ . '/vendor/autoload.php';

// 设置基本常量
define('APP_PATH', __DIR__ . '/application/');
define('ROOT_PATH', __DIR__ . '/');

// 初始化数据库连接
$config = [
    'type'            => 'mysql',
    'hostname'        => '127.0.0.1',
    'database'        => 'rongye',
    'username'        => 'root',
    'password'        => 'root',
    'hostport'        => '3306',
    'prefix'          => 'sys_',
    'charset'         => 'utf8',
];

\think\Db::setConfig($config);

echo "=== 触发订单108分润处理 ===\n\n";

try {
    // 检查订单状态
    $order = \think\Db::name('shopro_order')->where('id', 108)->find();
    if (!$order) {
        echo "错误：订单108不存在\n";
        exit(1);
    }
    
    echo "订单信息：\n";
    echo "- 订单ID: {$order['id']}\n";
    echo "- 用户ID: {$order['user_id']}\n";
    echo "- 订单金额: {$order['order_amount']}\n";
    echo "- 状态: {$order['status']}\n\n";
    
    // 检查用户激活状态
    $user = \think\Db::name('user')->where('id', $order['user_id'])->find();
    echo "购买用户状态：\n";
    echo "- 用户ID: {$user['id']}\n";
    echo "- 用户名: {$user['username']}\n";
    echo "- 激活状态: " . ($user['is_active'] ? '已激活' : '未激活') . "\n";
    echo "- 激活到期时间: {$user['active_expire_time']}\n\n";
    
    // 手动调用分润处理
    echo "开始处理分润...\n";
    
    // 这里需要手动实现简化的分润逻辑，因为完整的类加载比较复杂
    // 我们直接用SQL来模拟分润处理的关键步骤
    
    \think\Db::startTrans();
    
    // 1. 激活用户（模拟 processUserActiveStatus）
    $current_time = time();
    $expire_time = $current_time + (30 * 24 * 3600); // 30天后过期
    
    \think\Db::name('user')->where('id', $user['id'])->update([
        'is_active' => 1,
        'active_start_time' => $current_time,
        'active_expire_time' => $expire_time,
        'last_purchase_time' => $current_time
    ]);
    
    echo "✅ 用户激活完成\n";
    
    // 2. 给邀请人发放直销奖励（模拟 giveDirectSalesBonus）
    if ($user['parent_user_id'] > 0) {
        $inviter = \think\Db::name('user')->where('id', $user['parent_user_id'])->find();
        if ($inviter && $inviter['is_active'] == 1 && $inviter['active_expire_time'] > $current_time) {
            $direct_bonus = $order['order_amount'] * 0.20; // 20%直销奖励
            
            // 更新邀请人余额
            \think\Db::name('user')->where('id', $inviter['id'])->setInc('money', $direct_bonus);
            
            // 记录钱包日志
            \think\Db::name('shopro_user_wallet_log')->insert([
                'user_id' => $inviter['id'],
                'type' => 'money',
                'event' => 'order_buy_bonus',
                'amount' => $direct_bonus,
                'before' => $inviter['money'],
                'after' => $inviter['money'] + $direct_bonus,
                'memo' => '直接销售奖励 20%',
                'ext' => json_encode(['order_id' => $order['id']]),
                'oper_type' => 'system',
                'oper_id' => 0,
                'createtime' => $current_time
            ]);
            
            echo "✅ 邀请人直销奖励发放完成: {$direct_bonus}元\n";
        }
    }
    
    // 3. 给购买者发放团队奖励（模拟 giveBuyerTeamBonus）
    // 拓业顾问(group_id=3)有25%团队奖励
    if ($user['group_id'] == 3) {
        $team_bonus = $order['order_amount'] * 0.25; // 25%团队奖励
        
        // 更新购买者余额
        \think\Db::name('user')->where('id', $user['id'])->setInc('money', $team_bonus);
        
        // 记录钱包日志
        \think\Db::name('shopro_user_wallet_log')->insert([
            'user_id' => $user['id'],
            'type' => 'money',
            'event' => 'order_team_bonus',
            'amount' => $team_bonus,
            'before' => $user['money'],
            'after' => $user['money'] + $team_bonus,
            'memo' => '拓业顾问购买者团队奖励 25%',
            'ext' => json_encode(['order_id' => $order['id']]),
            'oper_type' => 'system',
            'oper_id' => 0,
            'createtime' => $current_time
        ]);
        
        echo "✅ 购买者团队奖励发放完成: {$team_bonus}元\n";
    }
    
    // 4. 更新累计分润字段
    $total_bonus = \think\Db::name('shopro_user_wallet_log')
        ->where('user_id', $user['id'])
        ->where('type', 'money')
        ->whereIn('event', ['order_buy_bonus', 'order_invite_bonus', 'order_team_bonus', 'order_level_bonus', 'order_ping_bonus', 'order_subsidy'])
        ->where('amount', '>', 0)
        ->sum('amount');
    
    \think\Db::name('user')->where('id', $user['id'])->update([
        'total_bonus_received' => $total_bonus
    ]);
    
    echo "✅ 累计分润字段更新完成: {$total_bonus}元\n";
    
    \think\Db::commit();
    
    echo "\n分润处理完成！\n\n";
    
    // 验证结果
    echo "=== 验证结果 ===\n";
    
    $user_after = \think\Db::name('user')->where('id', $user['id'])->find();
    echo "购买用户最终状态：\n";
    echo "- 激活状态: " . ($user_after['is_active'] ? '已激活' : '未激活') . "\n";
    echo "- 余额: {$user_after['money']}元\n";
    echo "- 累计分润: {$user_after['total_bonus_received']}元\n\n";
    
    $bonus_logs = \think\Db::name('shopro_user_wallet_log')
        ->where('ext', 'like', '%"order_id":108%')
        ->where('type', 'money')
        ->select();
    
    echo "分润记录：\n";
    foreach ($bonus_logs as $log) {
        $log_user = \think\Db::name('user')->where('id', $log['user_id'])->find();
        echo "- 用户{$log['user_id']}({$log_user['username']}): {$log['event']} +{$log['amount']}元\n";
    }
    
} catch (Exception $e) {
    \think\Db::rollback();
    echo "错误: " . $e->getMessage() . "\n";
}
